package com.niceloo.cmc.ex.utils;

import com.niceloo.cmc.ex.entity.CdrRecord;
import com.niceloo.cmc.ex.pojo.response.CdrResponse;
import okhttp3.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.DeserializationFeature;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;

public class YxCallRecordDownloader {

    private static final String BASE_URL = "http://47.107.129.78/openapi/V2.0.6/getCdrList";
    private static final String CUSTOMER = "C39";  // 固定客户标识
    private static final String PASSWORD = "E2F53233FBA3BE7E304518E04451CB763CAD535F";  // 替换为真实密码
    private static final String START_TIME = "2025-04-01 00:00:00";
    private static final String END_TIME = "2025-06-30 23:59:59";

    private static final OkHttpClient client = new OkHttpClient();
    private static final ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    private static final Logger log = LoggerFactory.getLogger(YxCallRecordDownloader.class);

    public static void main(String[] args) throws Exception {
        List<String> userDataList = readLinesFromFile("userdata.txt");
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("亿讯通话记录");

        createHeader(sheet);
        int rowNum = 1;

        // 每批最多 1000 个 userData
        final int BATCH_SIZE = 200;
        String startKey = null;

        for (int i = 0; i < userDataList.size(); i += BATCH_SIZE) {
            int end = Math.min(i + BATCH_SIZE, userDataList.size());
            List<String> batch = userDataList.subList(i, end);

            String jsonBody = buildRequestBodyWithBatch(batch, startKey);
            String responseBody = sendRequest(jsonBody);
            List<CdrRecord> records = parseResponse(responseBody);

            for (CdrRecord record : records) {
                Row row = sheet.createRow(rowNum++);
                writeRow(record, row);
            }
        }

        try (FileOutputStream fos = new FileOutputStream("output.xlsx")) {
            workbook.write(fos);
        }

        System.out.println("✅ 通话记录已成功导出到 output.xlsx");
    }


    private static void createHeader(Sheet sheet) {
        Row headerRow = sheet.createRow(0);
        int col = 0;
        headerRow.createCell(col++).setCellValue("话单ID");
        headerRow.createCell(col++).setCellValue("坐席工号");
        headerRow.createCell(col++).setCellValue("业务类型");
        headerRow.createCell(col++).setCellValue("开始时间");
        headerRow.createCell(col++).setCellValue("振铃时间");
        headerRow.createCell(col++).setCellValue("接通时间");
        headerRow.createCell(col++).setCellValue("主叫号码");
        headerRow.createCell(col++).setCellValue("被叫号码");
        headerRow.createCell(col++).setCellValue("通话时长(秒)");
        headerRow.createCell(col++).setCellValue("费用");
        headerRow.createCell(col++).setCellValue("挂断原因");
        headerRow.createCell(col++).setCellValue("录音文件");
        headerRow.createCell(col++).setCellValue("话单唯一标识");
        headerRow.createCell(col++).setCellValue("用户数据");
        headerRow.createCell(col++).setCellValue("任务ID");
        headerRow.createCell(col++).setCellValue("结束时间");
        headerRow.createCell(col++).setCellValue("呼叫结果");
        headerRow.createCell(col++).setCellValue("省份");
        headerRow.createCell(col++).setCellValue("城市");
        headerRow.createCell(col++).setCellValue("运营商");
        headerRow.createCell(col++).setCellValue("按键信息");
        headerRow.createCell(col++).setCellValue("分析结果");
        headerRow.createCell(col++).setCellValue("类型结果");
        headerRow.createCell(col++).setCellValue("企业标识");
        headerRow.createCell(col++).setCellValue("客户标签");
        headerRow.createCell(col++).setCellValue("错误信息");
        headerRow.createCell(col++).setCellValue("中文错误信息");
        headerRow.createCell(col++).setCellValue("中继类型");
        headerRow.createCell(col++).setCellValue("中继索引");
        headerRow.createCell(col++).setCellValue("flags");
        headerRow.createCell(col++).setCellValue("中继名称");
        headerRow.createCell(col++).setCellValue("中继对端IP");
        headerRow.createCell(col++).setCellValue("中继用户名");
    }

    private static void writeRow(CdrRecord record, Row row) {
        int col = 0;
        row.createCell(col++).setCellValue(record.getKey());
        row.createCell(col++).setCellValue(record.getAgent());
        row.createCell(col++).setCellValue(record.getServiceType());
        row.createCell(col++).setCellValue(record.getStartTime());
        row.createCell(col++).setCellValue(record.getRingTime());
        row.createCell(col++).setCellValue(record.getAnswerTime());
        row.createCell(col++).setCellValue(record.getCaller());
        row.createCell(col++).setCellValue(record.getCallee());
        row.createCell(col++).setCellValue(record.getTimeLength());
        row.createCell(col++).setCellValue(record.getFee());
        row.createCell(col++).setCellValue(record.getReleaseCause());
        row.createCell(col++).setCellValue(record.getFilename() != null ? record.getFilename() : "");
        row.createCell(col++).setCellValue(record.getSession());
        row.createCell(col++).setCellValue(record.getUserData());
        row.createCell(col++).setCellValue(record.getTaskID());
        row.createCell(col++).setCellValue(record.getByeTime());
        row.createCell(col++).setCellValue(record.getResult());
        row.createCell(col++).setCellValue(record.getArea());
        row.createCell(col++).setCellValue(record.getCity());
        row.createCell(col++).setCellValue(record.getSpName());
        row.createCell(col++).setCellValue(record.getKeyPress());
        row.createCell(col++).setCellValue(record.getCallResult());
        row.createCell(col++).setCellValue(record.getTypeResult());
        row.createCell(col++).setCellValue(record.getCustomer());
        row.createCell(col++).setCellValue(record.getLabel() != null ? String.join(",", record.getLabel()) : "");
        row.createCell(col++).setCellValue(record.getErrMsg());
        row.createCell(col++).setCellValue(record.getErrMsgCN());
        row.createCell(col++).setCellValue(record.getTrunkType());
        row.createCell(col++).setCellValue(record.getTrunkIndex());
        row.createCell(col++).setCellValue(record.getFlags());
        row.createCell(col++).setCellValue(record.getTrunkName());
        row.createCell(col++).setCellValue(record.getTrunkPeerIP());
        row.createCell(col++).setCellValue(record.getTrunkUsername());
    }

    private static List<String> readLinesFromFile(String path) throws IOException {
        return Files.readAllLines(Paths.get(path));
    }

    private static String buildRequestBodyWithBatch(List<String> userDataList,String startKey) {
        String timestamp = AuthUtil.generateTimestamp();
        String seq = AuthUtil.generateSeq();
        String digest = AuthUtil.generateDigest(CUSTOMER, timestamp, seq, PASSWORD);

        Map<String, Object> authMap = new HashMap<>();
        authMap.put("customer", CUSTOMER);
        authMap.put("timestamp", timestamp);
        authMap.put("seq", seq);
        authMap.put("digest", digest);

        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("userDataList", userDataList);
        requestMap.put("startTime", START_TIME);
        requestMap.put("endTime", END_TIME);

        Map<String, Object> rootMap = new HashMap<>();
        rootMap.put("authentication", authMap);
        rootMap.put("request", requestMap);

        try {
            return objectMapper.writeValueAsString(rootMap);
        } catch (Exception e) {
            throw new RuntimeException("JSON 序列化失败", e);
        }
    }


    private static String sendRequest(String jsonBody) throws IOException {
        Request request = new Request.Builder()
                .url(BASE_URL)
                .post(RequestBody.create(MediaType.parse("application/json; charset=utf-8"), jsonBody))
                .addHeader("Accept", "application/json")
                .addHeader("Content-Type", "application/json")
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) throw new IOException("HTTP 错误: " + response.code());

            ResponseBody body = response.body();
            if (body == null) throw new IOException("空响应体");

            return body.string();
        }
    }

    private static List<CdrRecord> parseResponse(String json) throws Exception {
        CdrResponse response = objectMapper.readValue(json, CdrResponse.class);
        if (response.result.error != 0) {
            throw new RuntimeException("API 调用失败: " + response.result.msg);
        }
        return response.data.response.cdr;
    }
}
